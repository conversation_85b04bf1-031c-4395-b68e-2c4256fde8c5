import os
import librosa
import json
import pickle
import warnings
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Union
import numpy as np
import torch
from datasets import load_dataset, Dataset, DatasetDict
from transformers import (
    Wav2Vec2ForCTC,
    Wav2Vec2Processor,
    Wav2Vec2FeatureExtractor,
    Wav2Vec2CTCTokenizer,
    TrainingArguments,
    Trainer,
    EarlyStoppingCallback,
    TrainerCallback
)
from dataclasses import dataclass
from torch.nn.utils.rnn import pad_sequence
import evaluate
from deep_utils import warmup_cosine
import matplotlib.pyplot as plt
import signal
from contextlib import contextmanager
import time
from tqdm import tqdm

# Configure comprehensive logging system
def setup_logging():
    """Setup comprehensive logging for full dataset training"""
    log_filename = f"asr_training_full_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # Clear any existing handlers
    root_logger.handlers.clear()

    # File handler for detailed logging
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)

    # Console handler for important messages
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # Configure specific loggers
    logging.getLogger("transformers").setLevel(logging.WARNING)
    logging.getLogger("datasets").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    warnings.filterwarnings("ignore")

    return log_filename

# Setup logging
log_filename = setup_logging()
logger = logging.getLogger(__name__)

# Global corruption counter for silent error handling
corruption_counter = 0

def log_memory_usage(stage_name):
    """Log current memory usage"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        logger.info(f"[{stage_name}] Memory usage: {memory_mb:.1f} MB")
    except ImportError:
        logger.debug("psutil not available for memory monitoring")

def log_system_info():
    """Log system information at startup"""
    try:
        import psutil
        logger.info("=== SYSTEM INFORMATION ===")
        logger.info(f"CPU cores: {psutil.cpu_count()}")
        memory = psutil.virtual_memory()
        logger.info(f"Total RAM: {memory.total / 1024**3:.1f} GB")
        logger.info(f"Available RAM: {memory.available / 1024**3:.1f} GB")

        # GPU information
        if torch.cuda.is_available():
            logger.info(f"CUDA available: {torch.cuda.device_count()} GPU(s)")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                logger.info(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            logger.info("CUDA not available - using CPU")
        logger.info("=" * 30)
    except ImportError:
        logger.info("System monitoring not available")

@contextmanager
def timeout_context(seconds):
    """Context manager for timeout operations"""
    def timeout_handler(signum, frame):
        raise TimeoutError(f"Operation timed out after {seconds} seconds")

    # Set the signal handler
    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(seconds)

    try:
        yield
    finally:
        # Restore the old signal handler
        signal.alarm(0)
        signal.signal(signal.SIGALRM, old_handler)

def dump_pickle(path, obj):
    """Save object to pickle file"""
    with open(path, 'wb') as f:
        pickle.dump(obj, f)

def load_pickle(path):
    """Load object from pickle file"""
    with open(path, 'rb') as f:
        return pickle.load(f)

@dataclass
class DataCollatorCTCWithPadding:
    """
    Data collator that will dynamically pad the inputs received and prepare labels for CTC loss.
    """
    processor: Wav2Vec2Processor
    padding: Union[bool, str] = True
    max_length: Union[int, None] = None
    max_length_labels: Union[int, None] = None
    pad_to_multiple_of: Union[int, None] = None
    pad_to_multiple_of_labels: Union[int, None] = None

    def __call__(self, features: List[Dict[str, Union[List[int], torch.Tensor]]]) -> Dict[str, torch.Tensor]:
        # split inputs and labels since they have to be of different lengths and need
        # different padding methods
        input_features = [{"input_values": feature["input_values"]} for feature in features]
        label_features = [{"input_ids": feature["labels"]} for feature in features]

        batch = self.processor.feature_extractor.pad(
            input_features,
            padding=self.padding,
            max_length=self.max_length,
            pad_to_multiple_of=self.pad_to_multiple_of,
            return_tensors="pt",
        )
        
        with self.processor.tokenizer.as_target_tokenizer():
            labels_batch = self.processor.tokenizer.pad(
                label_features,
                padding=self.padding,
                max_length=self.max_length_labels,
                pad_to_multiple_of=self.pad_to_multiple_of_labels,
                return_tensors="pt",
            )

        # replace padding with -100 to ignore loss correctly
        labels = labels_batch["input_ids"].masked_fill(labels_batch.attention_mask.ne(1), -100)

        batch["labels"] = labels

        return batch

def create_vocabulary_and_tokenizer(dataset, output_dir):
    """Create vocabulary and tokenizer from dataset text labels"""
    print("🔤 Creating vocabulary and tokenizer...")
    
    # Extract all unique characters from the text labels
    all_text = []
    for split in dataset.values():
        all_text.extend(split['label'])
    
    # Get unique characters
    vocab_chars = set()
    for text in all_text:
        vocab_chars.update(list(text))
    
    # Sort characters for consistent ordering
    vocab_list = sorted(list(vocab_chars))
    
    # Add special tokens
    vocab_dict = {
        "[PAD]": 0,
        "[UNK]": 1,
        "[CTC]": 2,  # CTC blank token
    }
    
    # Add characters to vocabulary
    for i, char in enumerate(vocab_list):
        vocab_dict[char] = i + 3
    
    print(f"📊 Vocabulary size: {len(vocab_dict)} characters")
    print(f"📋 Sample characters: {vocab_list[:20]}{'...' if len(vocab_list) > 20 else ''}")
    
    # Save vocabulary
    vocab_file = os.path.join(output_dir, "vocab.json")
    with open(vocab_file, 'w', encoding='utf-8') as f:
        json.dump(vocab_dict, f, ensure_ascii=False, indent=2)
    
    # Create tokenizer
    tokenizer = Wav2Vec2CTCTokenizer(
        vocab_file,
        unk_token="[UNK]",
        pad_token="[PAD]",
        word_delimiter_token="|",
        do_lower_case=False,
    )
    
    return tokenizer, vocab_dict

def prepare_dataset(batch, tokenizer, feature_extractor):
    """Prepare dataset batch for training"""
    global corruption_counter

    # Load and process audio
    try:
        audio_path = batch["audio_path"]
        audio_array, _ = librosa.load(audio_path, sr=16000)

        # Check if audio is valid (not empty or too short)
        if len(audio_array) < 1000:  # Less than ~0.06 seconds at 16kHz
            raise ValueError("Audio too short")

        # Explicitly cast audio_array to float32
        audio_array = audio_array.astype(np.float32)

        # Process audio with feature extractor
        input_values = feature_extractor(
            audio_array,
            sampling_rate=16000,
            max_length=16000 * 30,  # 30 seconds max
            truncation=True
        ).input_values[0]

        # Explicitly cast input_values to float32
        input_values = input_values.astype(np.float32)

        batch["input_values"] = input_values

        # Process text with tokenizer
        with tokenizer.as_target_tokenizer():
            batch["labels"] = tokenizer(batch["label"]).input_ids

        return batch

    except Exception as e:
        # Handle corrupted files by returning minimal valid data to avoid filtering issues
        corruption_counter += 1
        logger.debug(f"Skipping corrupted file {batch.get('audio_path', 'unknown')}: {str(e)}")

        # Return minimal valid data structure instead of None to avoid filtering bottlenecks
        # This will be a very short audio sample that won't significantly impact training
        minimal_audio = np.zeros(1600, dtype=np.float32)  # 0.1 seconds of silence
        minimal_input_values = feature_extractor(
            minimal_audio,
            sampling_rate=16000,
            return_tensors="np"
        ).input_values[0]

        # Use a minimal label (single character)
        with tokenizer.as_target_tokenizer():
            minimal_labels = tokenizer(" ").input_ids  # Single space character

        return {
            "input_values": minimal_input_values.astype(np.float32),
            "labels": minimal_labels
        }

def compute_metrics(pred, processor):
    """Compute WER and CER metrics for evaluation"""
    wer_metric = evaluate.load("wer")
    cer_metric = evaluate.load("cer")

    pred_logits = pred.predictions
    pred_ids = np.argmax(pred_logits, axis=-1)

    pred.label_ids[pred.label_ids == -100] = processor.tokenizer.pad_token_id

    pred_str = processor.batch_decode(pred_ids)
    label_str = processor.batch_decode(pred.label_ids, group_tokens=False)

    wer = wer_metric.compute(predictions=pred_str, references=label_str)
    cer = cer_metric.compute(predictions=pred_str, references=label_str)

    return {"wer": wer, "cer": cer}

def save_training_report(training_start_time, training_end_time, total_files, valid_files, 
                        corruption_counter, final_metrics, output_dir):
    """Save comprehensive training report"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    training_duration = (training_end_time - training_start_time).total_seconds() / 60
    
    # Create reports directory
    reports_dir = "training_reports"
    os.makedirs(reports_dir, exist_ok=True)
    
    # Training summary
    summary = f"""============================================================
SURAH FATIHA ASR TRAINING SUMMARY
============================================================

Training Timestamp: {timestamp}
Training Duration: {training_duration:.2f} minutes

DATA PROCESSING STATISTICS:
------------------------------
Total files processed: {total_files:,}
Valid files: {valid_files:,}
Corrupted files: {corruption_counter:,}
Corruption rate: {round((corruption_counter / total_files) * 100, 2)}%

MODEL CONFIGURATION:
--------------------
Base model: facebook/wav2vec2-base
Model type: Wav2Vec2ForCTC (ASR)
Task: Automatic Speech Recognition
Target language: Arabic (Surah Al-Fatiha)
Vocabulary size: Character-level tokenization

FINAL TRAINING METRICS:
-------------------------
{json.dumps(final_metrics, indent=2) if final_metrics else 'Training metrics not available'}

============================================================
Training completed successfully!
============================================================"""
    
    # Save summary
    summary_file = os.path.join(reports_dir, f"asr_training_summary_{timestamp}.txt")
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print(f"📊 Training report saved to: {summary_file}")
    return summary_file

if __name__ == "__main__":
    print("🎙️ Starting Surah Al-Fatiha ASR Training with Wav2Vec2 - FULL DATASET")
    print("=" * 70)
    print(f"📝 Log file: {log_filename}")
    print()

    # Log system information
    log_system_info()
    logger.info("Starting full dataset ASR training pipeline")
    log_memory_usage("Startup")

    training_start_time = datetime.now()

    # Configuration
    train_path = "/nvme0n1-disk/ASR_Training/ASR-Wav2vec-Finetune/dataset/train_updated.txt"
    test_path = "/nvme0n1-disk/ASR_Training/ASR-Wav2vec-Finetune/dataset/test_updated.txt"
    output_dir = "/nvme0n1-disk/ASR_Training/ASR-Wav2vec-Finetune/asr_arabic_pretrained"


    # Training hyperparameters - Optimized for full dataset training
    # Determine optimal batch size based on available GPU memory
    if torch.cuda.is_available():
        gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if gpu_memory_gb >= 32:  # A100 or similar
            train_bs = 16
        elif gpu_memory_gb >= 16:  # V100 or similar
            train_bs = 12
        else:  # Smaller GPUs
            train_bs = 8
    else:
        train_bs = 4  # CPU training

    epochs = 10     # Sufficient epochs for full dataset convergence
    lr = 3e-5       # Conservative learning rate for stable training on large dataset

    logger.info(f"Training configuration for full dataset:")
    logger.info(f"  - Batch size: {train_bs}")
    logger.info(f"  - Epochs: {epochs}")
    logger.info(f"  - Learning rate: {lr}")
    logger.info(f"  - Device: {'GPU' if torch.cuda.is_available() else 'CPU'}")

    print(f"📂 Training data: {train_path}")
    print(f"📂 Test data: {test_path}")
    print(f"📁 Output directory: {output_dir}")
    print()

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(f"{output_dir}/best", exist_ok=True)

    def load_text_dataset(train_path: str, test_path: str):
        def parse_line(line):
            parts = line.strip().split('|', 1)
            if len(parts) == 2:
                path, transcription = parts
                return {"audio_path": path, "label": transcription}
            else:
                return None

        def is_valid_audio(path, timeout_seconds=5):
            """Validate audio file with timeout to prevent hanging"""
            try:
                # Check if file exists first
                if not os.path.exists(path):
                    logger.debug(f"Audio file not found: {path}")
                    return False

                # Check file size (skip very large files that might cause issues)
                file_size = os.path.getsize(path)
                if file_size > 50 * 1024 * 1024:  # 50MB limit
                    logger.debug(f"Audio file too large ({file_size} bytes): {path}")
                    return False

                # Use timeout context to prevent hanging on corrupted files
                with timeout_context(timeout_seconds):
                    audio, _ = librosa.load(path, sr=16000, duration=30)  # Limit to 30 seconds
                    # Check if audio has reasonable length
                    if len(audio) < 1000:  # Less than ~0.06 seconds at 16kHz
                        logger.debug(f"Audio file too short: {path}")
                        return False
                    return True
            except (TimeoutError, Exception) as e:
                logger.debug(f"Audio validation failed for {path}: {str(e)}")
                return False

        # Check if dataset files exist
        if not os.path.exists(train_path):
            logger.error(f"Training dataset file not found: {train_path}")
            raise FileNotFoundError(f"Training dataset file not found: {train_path}")

        if not os.path.exists(test_path):
            logger.error(f"Test dataset file not found: {test_path}")
            raise FileNotFoundError(f"Test dataset file not found: {test_path}")

        logger.info("Loading training dataset...")
        with open(train_path, 'r', encoding='utf-8') as f:
            train_lines = [line.strip() for line in f if line.strip()]

        logger.info("Loading test dataset...")
        with open(test_path, 'r', encoding='utf-8') as f:
            test_lines = [line.strip() for line in f if line.strip()]

        logger.info(f"Found {len(train_lines)} training lines and {len(test_lines)} test lines")

        # Process full dataset for production training
        logger.info(f"Processing full dataset: {len(train_lines)} training lines and {len(test_lines)} test lines")
        log_memory_usage("Dataset Loading")

        # Process training data with progress bar
        train_data = []
        logger.info("Validating training audio files...")
        for i, line in enumerate(train_lines):
            if i % 50 == 0:  # Progress update every 50 files
                logger.info(f"Processing training file {i+1}/{len(train_lines)}")

            try:
                path, transcription = line.split('|', 1)
                if is_valid_audio(path):
                    train_data.append(parse_line(line))
                else:
                    logger.debug(f"Invalid audio file: {path}")
            except ValueError as e:
                logger.warning(f"Malformed line in training data: {line}")
                continue

        # Process test data with progress bar
        test_data = []
        logger.info("Validating test audio files...")
        for i, line in enumerate(test_lines):
            if i % 50 == 0:  # Progress update every 50 files
                logger.info(f"Processing test file {i+1}/{len(test_lines)}")

            try:
                path, transcription = line.split('|', 1)
                if is_valid_audio(path):
                    test_data.append(parse_line(line))
                else:
                    logger.debug(f"Invalid audio file: {path}")
            except ValueError as e:
                logger.warning(f"Malformed line in test data: {line}")
                continue

        logger.info(f"Valid training samples: {len(train_data)}")
        logger.info(f"Valid test samples: {len(test_data)}")

        if len(train_data) == 0:
            raise ValueError("No valid training samples found!")
        if len(test_data) == 0:
            raise ValueError("No valid test samples found!")

        train_dataset = Dataset.from_list(train_data)
        test_dataset = Dataset.from_list(test_data)

        return DatasetDict({"train": train_dataset, "test": test_dataset})

    # Load dataset from text files
    print("📊 Loading dataset from text files...")
    train_path = "/nvme0n1-disk/ASR_Training/ASR-Wav2vec-Finetune/dataset/train_updated.txt"
    test_path = "/nvme0n1-disk/ASR_Training/ASR-Wav2vec-Finetune/dataset/test_updated.txt"
    dataset = load_text_dataset(train_path, test_path)
    print(f"✅ Dataset loaded successfully!")
    print(f"📊 Training samples: {len(dataset['train']):,}")
    print(f"📊 Test samples: {len(dataset['test']):,}")
    print()


    # Create vocabulary and tokenizer
    tokenizer, vocab_dict = create_vocabulary_and_tokenizer(dataset, f"{output_dir}/best")

    # Create feature extractor
    print("🔧 Creating feature extractor...")
    feature_extractor = Wav2Vec2FeatureExtractor.from_pretrained("elgeish/wav2vec2-large-xlsr-53-arabic")

    # Create processor
    processor = Wav2Vec2Processor(feature_extractor=feature_extractor, tokenizer=tokenizer)

    # Save processor
    processor.save_pretrained(f"{output_dir}/best")

    print("✅ Processor created and saved!")
    print()

    # Prepare dataset
    print("🔄 Processing audio files...")
    total_files = len(dataset["train"]) + len(dataset["test"])

    # Process training data
    def prepare_train_dataset(batch):
        return prepare_dataset(batch, tokenizer, feature_extractor)

    # Apply preprocessing with progress tracking
    # Apply preprocessing with progress tracking, caching, and reduced parallel processing
    logger.info("Starting audio preprocessing...")

    encoded_dataset = dataset.map(
        prepare_train_dataset,
        remove_columns=["audio_path", "label"],
        num_proc=1,  # Use single process to prevent hanging
        cache_file_names={
            "train": f"{output_dir}/cached_train.arrow",
            "test": f"{output_dir}/cached_test.arrow"
        },
        desc="Processing audio files",
        load_from_cache_file=True,  # Use cache if available
        writer_batch_size=100  # Process in smaller batches
    )
    logger.info("Audio preprocessing completed!")

    # Note: Removed the separate filtering step that was causing performance bottlenecks
    # Corrupted files are now handled by returning minimal valid data in prepare_dataset

    # Display corruption statistics
    valid_files = len(encoded_dataset["train"]) + len(encoded_dataset["test"])
    print(f"✅ Audio processing completed!")
    print(f"📊 Files processed: {total_files:,}")
    print(f"📊 Valid files: {valid_files:,}")
    print(f"📊 Corrupted files: {corruption_counter:,}")
    print(f"📊 Corruption rate: {round((corruption_counter / total_files) * 100, 2)}%")
    print()

    # Create data collator
    data_collator = DataCollatorCTCWithPadding(processor=processor, padding=True)

    # Load model
    print("🤖 Loading Wav2Vec2ForCTC model...")
    model = Wav2Vec2ForCTC.from_pretrained(
        "elgeish/wav2vec2-large-xlsr-53-arabic",  # Arabic-specific model
        ctc_loss_reduction="mean",
        pad_token_id=processor.tokenizer.pad_token_id,
        vocab_size=len(processor.tokenizer),
        ignore_mismatched_sizes=True,  # Add this to avoid vocab size conflicts
    )

    # Don't freeze feature encoder for better ASR performance
    # model.freeze_feature_encoder()  # Commented out for better learning

    print(f"✅ Model loaded successfully!")
    print(f"📊 Vocabulary size: {len(processor.tokenizer)}")
    print()

    # Enhanced training setup for full dataset
    total_steps = int((np.ceil(len(encoded_dataset["train"]) / train_bs) * epochs))
    eval_steps = max(500, total_steps // 50)  # Evaluate 50 times during training
    save_steps = max(1000, total_steps // 20)  # Save 20 checkpoints during training
    logging_steps = max(100, total_steps // 200)  # Log 200 times during training
    warmup_steps = min(2000, total_steps // 10)  # 10% warmup

    logger.info(f"Training schedule for full dataset:")
    logger.info(f"  - Total steps: {total_steps:,}")
    logger.info(f"  - Eval steps: {eval_steps:,}")
    logger.info(f"  - Save steps: {save_steps:,}")
    logger.info(f"  - Logging steps: {logging_steps:,}")
    logger.info(f"  - Warmup steps: {warmup_steps:,}")

    print(f"📊 Total training steps: {total_steps:,}")
    log_memory_usage("Pre-Training")

    # Enhanced training arguments for full dataset
    training_args = TrainingArguments(
        output_dir=output_dir,
        group_by_length=True,  # Important for CTC training
        per_device_train_batch_size=train_bs,
        per_device_eval_batch_size=train_bs,
        gradient_accumulation_steps=2,  # Effective batch size = train_bs * 2
        evaluation_strategy="steps",
        eval_steps=eval_steps,
        save_strategy="steps",
        save_steps=save_steps,
        num_train_epochs=epochs,
        fp16=torch.cuda.is_available(),  # Only use fp16 with GPU
        gradient_checkpointing=True,
        dataloader_drop_last=True,
        logging_steps=logging_steps,
        save_total_limit=5,  # Keep more checkpoints for large training
        load_best_model_at_end=True,
        metric_for_best_model="wer",
        greater_is_better=False,  # Lower WER is better
        push_to_hub=False,
        disable_tqdm=False,
        dataloader_num_workers=2,  # Reduced to prevent memory issues
        remove_unused_columns=False,
        report_to=["tensorboard"],
        logging_dir=f"{output_dir}/logs",
        run_name=f"wav2vec2-arabic-full-{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        resume_from_checkpoint=True,  # Enable automatic resume
        dataloader_pin_memory=torch.cuda.is_available(),
        warmup_steps=warmup_steps,
        learning_rate=lr,
    )

    # Enhanced callbacks for full dataset training
    class ProgressLoggingCallback(TrainerCallback):
        """Custom callback for enhanced progress logging"""

        def __init__(self):
            self.start_time = None
            self.last_log_time = None

        def on_train_begin(self, args, state, control, **kwargs):
            self.start_time = time.time()
            self.last_log_time = self.start_time
            logger.info("🚀 Training started!")
            log_memory_usage("Training Start")

        def on_log(self, args, state, control, logs=None, **kwargs):
            if logs:
                current_time = time.time()
                elapsed = current_time - self.start_time
                since_last = current_time - self.last_log_time

                # Calculate progress
                progress = state.global_step / state.max_steps * 100
                eta_seconds = (elapsed / state.global_step) * (state.max_steps - state.global_step)
                eta_str = str(timedelta(seconds=int(eta_seconds)))

                # Log training metrics
                if 'train_loss' in logs:
                    logger.info(f"Step {state.global_step}/{state.max_steps} ({progress:.1f}%) - "
                              f"Loss: {logs['train_loss']:.4f} - "
                              f"LR: {logs.get('learning_rate', 0):.2e} - "
                              f"ETA: {eta_str}")

                # Log evaluation metrics
                if 'eval_wer' in logs:
                    logger.info(f"Evaluation - WER: {logs['eval_wer']:.4f}, CER: {logs.get('eval_cer', 0):.4f}")

                self.last_log_time = current_time

                # Periodic memory logging
                if state.global_step % (args.logging_steps * 10) == 0:
                    log_memory_usage(f"Step {state.global_step}")

        def on_save(self, args, state, control, **kwargs):
            logger.info(f"💾 Checkpoint saved at step {state.global_step}")

        def on_train_end(self, args, state, control, **kwargs):
            total_time = time.time() - self.start_time
            logger.info(f"✅ Training completed in {str(timedelta(seconds=int(total_time)))}")
            log_memory_usage("Training End")

    # Early stopping with increased patience for large dataset
    early_stopping = EarlyStoppingCallback(early_stopping_patience=5)  # More conservative for full dataset
    progress_callback = ProgressLoggingCallback()

    print("🚀 Starting training...")
    print(f"📊 Batch size: {train_bs}")
    print(f"📊 Learning rate: {lr}")
    print(f"📊 Max epochs: {epochs}")
    print(f"📊 Total steps: {total_steps:,}")
    print()

    # Create compute metrics function with processor
    def compute_metrics_with_processor(pred):
        return compute_metrics(pred, processor)

    # Create trainer with enhanced callbacks
    trainer = Trainer(
        model=model,
        data_collator=data_collator,
        args=training_args,
        compute_metrics=compute_metrics_with_processor,
        train_dataset=encoded_dataset["train"],
        eval_dataset=encoded_dataset["test"],
        tokenizer=processor.feature_extractor,
        callbacks=[early_stopping, progress_callback]
    )

    # Enhanced training with error handling and recovery
    print("🚀 Starting training...")
    print(f"📊 Batch size: {train_bs}")
    print(f"📊 Learning rate: {lr}")
    print(f"📊 Max epochs: {epochs}")
    print(f"📊 Total steps: {total_steps}")
    print()

    logger.info(f"Training started at {training_start_time}")
    log_memory_usage("Training Start")

    try:
        # Check for existing checkpoints
        checkpoint_dir = None
        if os.path.exists(output_dir):
            checkpoints = [d for d in os.listdir(output_dir) if d.startswith('checkpoint-')]
            if checkpoints:
                # Find the latest checkpoint
                checkpoint_numbers = [int(d.split('-')[1]) for d in checkpoints if d.split('-')[1].isdigit()]
                if checkpoint_numbers:
                    latest_checkpoint = max(checkpoint_numbers)
                    checkpoint_dir = os.path.join(output_dir, f'checkpoint-{latest_checkpoint}')
                    logger.info(f"Found existing checkpoint: {checkpoint_dir}")
                    print(f"🔄 Resuming from checkpoint: {checkpoint_dir}")

        # Start training (with automatic resume if checkpoint exists)
        if checkpoint_dir and os.path.exists(checkpoint_dir):
            train_result = trainer.train(resume_from_checkpoint=checkpoint_dir)
        else:
            train_result = trainer.train()

        training_end_time = datetime.now()
        logger.info(f"Training completed successfully at {training_end_time}")
        print("✅ Training completed!")

    except KeyboardInterrupt:
        logger.warning("Training interrupted by user")
        print("\n⚠️ Training interrupted by user. Saving current state...")
        trainer.save_model(f"{output_dir}/interrupted_checkpoint")
        training_end_time = datetime.now()
        train_result = None

    except Exception as e:
        logger.error(f"Training failed with error: {str(e)}")
        print(f"\n❌ Training failed: {str(e)}")
        # Save what we can
        try:
            trainer.save_model(f"{output_dir}/error_checkpoint")
            logger.info("Saved error checkpoint")
        except:
            logger.error("Could not save error checkpoint")
        training_end_time = datetime.now()
        train_result = None
        raise

    print()

    # Save the final model
    if train_result is not None:
        print("💾 Saving final model...")
        trainer.save_model(f"{output_dir}/best")
        logger.info("Final model saved successfully")

        # Save training metrics
        final_metrics = train_result.metrics if hasattr(train_result, 'metrics') else {}
        logger.info(f"Final training metrics: {final_metrics}")
    else:
        print("⚠️ Training was interrupted - no final model to save")
        final_metrics = {}

    # Save training report
    save_training_report(
        training_start_time,
        training_end_time,
        total_files,
        valid_files,
        corruption_counter,
        final_metrics,
        output_dir
    )

    print("🎉 Surah Al-Fatiha ASR training completed successfully!")
    print(f"📁 Model saved to: {output_dir}/best")
    print(f"⏱️  Total training time: {(training_end_time - training_start_time).total_seconds() / 60:.2f} minutes")
